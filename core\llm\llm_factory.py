"""
LLM工厂类 - 负责创建和配置完整的LLM实例
"""

from typing import Optional, Dict, Any
from .tongyi_client import TongyiQianwenClient
from ..processors.code_cleaner import CodeCleaner
from ..processors.chart_fixer import ChartFixer
from ..processors.metadata_processor import MetadataProcessor
from ..utils.config import TongyiConfig
from ..utils.logger import get_app_logger


class EnhancedTongyiLLM:
    """
    增强的通义千问LLM
    
    组合了API客户端和各种处理器，提供完整的LLM功能。
    """
    
    def __init__(
        self,
        client: TongyiQianwenClient,
        code_cleaner: Optional[CodeCleaner] = None,
        chart_fixer: Optional[ChartFixer] = None,
        metadata_processor: Optional[MetadataProcessor] = None,
        enable_logging: bool = True
    ):
        """
        初始化增强LLM
        
        Args:
            client: 通义千问API客户端
            code_cleaner: 代码清理器
            chart_fixer: 图表修复器
            metadata_processor: 元数据处理器
            enable_logging: 是否启用日志记录
        """
        self.client = client
        self.code_cleaner = code_cleaner or CodeCleaner(enable_logging)
        self.chart_fixer = chart_fixer or ChartFixer(enable_logging)
        self.metadata_processor = metadata_processor or MetadataProcessor(enable_logging)
        
        self.logger = get_app_logger() if enable_logging else None
        
        # 功能开关
        self.enable_chart_fix = True
        self.enable_metadata = True  # 启用元数据功能
    
    def analyze_data_with_context(self, instruction: str, context: str,
                                 conversation_context: Optional[Dict] = None,
                                 metadata: Optional[Dict] = None,
                                 table_name: str = "data") -> str:
        """
        基于对话上下文的数据分析

        Args:
            instruction: 用户指令
            context: 数据上下文
            conversation_context: 对话上下文信息
            metadata: 元数据
            table_name: 表名

        Returns:
            生成的代码
        """
        if self.logger:
            self.logger.info(f"开始上下文感知的数据分析 - 指令: {instruction[:50]}...")

        try:
            # 1. 构建增强的提示词
            if conversation_context:
                enhanced_prompt = self._build_contextual_prompt(
                    instruction, context, conversation_context, metadata, table_name
                )
            else:
                # 降级到原有方法
                return self.analyze_data(instruction, context, metadata, table_name)

            # 2. 调用LLM API
            response = self.client.call(instruction=enhanced_prompt, context="")
            raw_code = response.content

            if self.logger:
                self.logger.info(f"LLM响应获取成功 - tokens: {response.tokens_used}")

            # 3. 清理代码
            cleaned_code = self.code_cleaner.clean(raw_code)

            # 3.5. 验证引用性代码（新增）
            if conversation_context and conversation_context.get('references'):
                cleaned_code = self._validate_and_fix_reference_code(cleaned_code, conversation_context, instruction)

            # 4. 修复图表（如果启用）
            if self.enable_chart_fix:
                final_code = self.chart_fixer.fix_charts(cleaned_code, instruction)
            else:
                final_code = cleaned_code

            if self.logger:
                self.logger.info("上下文感知数据分析完成")

            return final_code

        except Exception as e:
            error_msg = f"上下文感知数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)

            # 降级到基础分析
            if self.logger:
                self.logger.info("降级到基础数据分析模式")
            return self.analyze_data(instruction, context, metadata, table_name)

    def _build_contextual_prompt(self, instruction: str, context: str,
                               conversation_context: Dict, metadata: Optional[Dict],
                               table_name: str) -> str:
        """
        构建包含对话上下文的提示词

        Args:
            instruction: 当前指令
            context: 数据上下文
            conversation_context: 对话上下文
            metadata: 元数据
            table_name: 表名

        Returns:
            增强的提示词
        """
        prompt_parts = [
            "你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。",
            f"当前数据信息：\n{context}",
        ]

        # 添加元数据信息
        if metadata and self.enable_metadata:
            metadata_text = self.metadata_processor.format_metadata_for_prompt(metadata)
            prompt_parts.append(f"数据元数据：\n{metadata_text}")

        # 添加对话摘要
        if conversation_context.get('has_summary') and conversation_context['summary']:
            summary = conversation_context['summary']
            prompt_parts.append(f"对话背景：{summary['summary_text']}")

            if summary['key_concerns']:
                prompt_parts.append(f"用户关注点：{', '.join(summary['key_concerns'])}")

        # 添加最近对话历史
        recent_rounds = conversation_context.get('recent_rounds', [])
        if recent_rounds:
            prompt_parts.append("最近的对话历史：")
            for i, round_data in enumerate(recent_rounds[-3:], 1):  # 最近3轮
                prompt_parts.append(f"第{i}轮 - 用户：{round_data['user_message']}")
                if round_data.get('code'):
                    code = round_data['code']

                    # 智能代码摘要：长代码只保留核心逻辑
                    if len(code) > 400:  # 超过400字符的代码进行优化
                        optimized_code = self._optimize_code_for_context(code)
                        prompt_parts.append(f"第{i}轮 - 核心逻辑：\n```python\n{optimized_code}\n```")
                    else:
                        prompt_parts.append(f"第{i}轮 - 生成代码：\n```python\n{code}\n```")

                    # 添加执行结果状态
                    exec_result = round_data.get('execution_result', {})
                    if exec_result.get('success'):
                        prompt_parts.append(f"第{i}轮 - 代码执行：成功")
                    elif exec_result:
                        prompt_parts.append(f"第{i}轮 - 代码执行：失败 - {exec_result.get('error', '未知错误')}")

        # 添加引用处理（优化版）
        references = conversation_context.get('references', {})
        if references:
            prompt_parts.append("引用信息：")
            for ref_type, ref_data in references.items():
                if ref_data and ref_data.get('success'):
                    ref_code = ref_data['code']
                    if len(ref_code) > 300:  # 引用代码也进行优化
                        optimized_ref_code = self._optimize_code_for_context(ref_code)
                        prompt_parts.append(f"之前的{ref_type}核心逻辑：\n```python\n{optimized_ref_code}\n```")
                    else:
                        prompt_parts.append(f"之前的{ref_type}代码：\n```python\n{ref_code}\n```")

        # 添加当前指令
        prompt_parts.extend([
            f"当前用户问题：{instruction}",
            "",
            "请基于以上对话历史和数据信息，生成相应的Python代码。",
            ""
        ])

        # 检查是否需要应用强制性引用规则
        reference_keywords = ['进一步', '基于', '在此基础上', '然后', '接下来', '继续', '扩展']
        has_reference_intent = any(kw in instruction for kw in reference_keywords)
        has_references = bool(conversation_context.get('references', {}))

        # 只有在确实有引用意图且有历史引用时才应用强制规则
        if has_reference_intent and has_references:
                prompt_parts.extend([
                    "🚨🚨🚨 CRITICAL: 检测到引用意图，以下规则必须100%严格执行 🚨🚨🚨",
                    "",
                    f"⚠️ 当前用户指令包含引用关键词: {[kw for kw in reference_keywords if kw in instruction]} → 这是强制引用信号！",
                    "",
                    "🔒 MANDATORY RULE 1 - 变量复用（违反此规则=错误）：",
                    "   ❌ 禁止：重新定义region_sales = df.groupby('地区')['销售额'].sum()",
                    "   ✅ 必须：直接使用已存在的region_sales变量",
                    "   ✅ 必须：在代码开头添加 if 'region_sales' not in locals(): region_sales = ...",
                    "",
                    "🔒 MANDATORY RULE 2 - 组合分析（违反此规则=错误）：",
                    "   ❌ 禁止：独立分析 salesperson_sales = df.groupby('销售员')['销售额'].sum()",
                    "   ✅ 必须：组合分析 region_salesperson = df.groupby(['地区', '销售员'])['销售额'].sum()",
                    "   ✅ 必须：基于region_sales变量进行扩展",
                    "",
                    "🔒 MANDATORY RULE 3 - 代码结构（必须按此模板）：",
                    "   第1行：确保变量存在 if 'region_sales' not in locals():",
                    "   第2行：组合分析 region_salesperson = df.groupby(['地区', '销售员'])...",
                    "   第3行：基于地区展示 for region in region_sales['地区']:",
                    "",
                    "🔒 MANDATORY TEMPLATE - 必须按此模板生成代码：",
                    "```python",
                    "# STEP 1: 确保历史变量存在（必须包含）",
                    "if 'region_sales' not in locals():",
                    "    region_sales = df.groupby('地区')['销售额'].sum().reset_index()",
                    "",
                    "# STEP 2: 组合分析（必须包含）",
                    "region_salesperson = df.groupby(['地区', '销售员'])['销售额'].sum().reset_index()",
                    "",
                    "# STEP 3: 基于地区展示（必须包含）",
                    "st.subheader('🌍 基于地区分析的销售员表现')",
                    "for region in region_sales['地区']:",
                    "    region_data = region_salesperson[region_salesperson['地区'] == region]",
                    "    st.write(f'📍 {region}地区销售员排名:')",
                    "    st.bar_chart(region_data.set_index('销售员')['销售额'])",
                    "```",
                    "",
                    "🚨 FINAL CHECK - 生成代码必须包含：",
                    "   ✅ if 'region_sales' not in locals():",
                    "   ✅ region_salesperson = df.groupby(['地区', '销售员'])",
                    "   ✅ for region in region_sales['地区']:",
                    "   ❌ 如果缺少任何一项，代码将被视为错误！"
                ])
        else:
            # 对于非引用的普通分析，使用标准指导原则
            prompt_parts.extend([
                "重要指导原则：",
                "1. 根据用户需求生成清晰、准确的数据分析代码",
                "2. 确保代码能够独立运行，包含必要的导入语句",
                "3. 使用适当的可视化方式展示分析结果",
                "4. 保持代码的可读性和逻辑性"
            ])

        final_prompt = "\n\n".join(prompt_parts)

        # 详细日志输出 - 发送给LLM的完整提示词
        if self.logger:
            self.logger.info("=" * 100)
            self.logger.info("🤖 发送给大模型的完整提示词:")
            self.logger.info("-" * 100)
            self.logger.info(final_prompt)
            self.logger.info("-" * 100)
            self.logger.info(f"提示词总长度: {len(final_prompt)} 字符")
            self.logger.info(f"预估Token数: {len(final_prompt) // 4} tokens")
            self.logger.info("=" * 100)

        return final_prompt

    def _validate_and_fix_reference_code(self, code: str, conversation_context: Dict, instruction: str) -> str:
        """
        验证并修复引用性代码，确保符合指导原则
        """
        if self.logger:
            self.logger.info("🔍 开始验证引用性代码质量")

        # 检查是否包含"进一步"等引用关键词
        reference_keywords = ['进一步', '基于', '在此基础上', '然后', '接下来']
        has_reference_intent = any(kw in instruction for kw in reference_keywords)

        if not has_reference_intent:
            return code

        # 获取历史变量信息
        references = conversation_context.get('references', {})
        analysis_ref = references.get('analysis', {})
        historical_code = analysis_ref.get('code', '')

        # 检查历史代码中的关键变量
        key_variables = []
        if 'region_sales' in historical_code:
            key_variables.append('region_sales')
        if 'product_sales' in historical_code:
            key_variables.append('product_sales')

        if not key_variables:
            if self.logger:
                self.logger.warning("⚠️ 未找到历史关键变量，跳过验证")
            return code

        # 验证生成的代码
        validation_issues = []

        # 检查1: 是否复用了历史变量
        has_variable_reuse = False
        for var in key_variables:
            if f"if '{var}' not in locals():" in code or var in code:
                has_variable_reuse = True
                break

        if not has_variable_reuse:
            validation_issues.append(f"❌ 缺少变量复用: 应该复用 {key_variables}")

        # 检查2: 是否实现了组合分析
        has_combination_analysis = False
        combination_patterns = [
            "groupby(['地区', '销售员'])",
            'groupby(["地区", "销售员"])',
            "groupby(['地区','销售员'])",
            'groupby(["地区","销售员"])'
        ]

        for pattern in combination_patterns:
            if pattern in code:
                has_combination_analysis = True
                break

        if not has_combination_analysis and '销售员' in instruction:
            validation_issues.append("❌ 缺少组合分析: 应该使用 groupby(['地区', '销售员'])")

        # 检查3: 是否基于历史变量进行展示
        has_based_display = False
        for var in key_variables:
            if f"for region in {var}['地区']:" in code or f"for region in {var}[\"地区\"]:" in code:
                has_based_display = True
                break

        if not has_based_display and 'region_sales' in key_variables:
            validation_issues.append("❌ 缺少基于展示: 应该使用 for region in region_sales['地区']:")

        # 如果有验证问题，尝试修复
        if validation_issues:
            if self.logger:
                self.logger.warning(f"🚨 发现代码质量问题: {len(validation_issues)}个")
                for issue in validation_issues:
                    self.logger.warning(f"   {issue}")
                self.logger.info("🔧 尝试自动修复代码...")

            # 生成修复后的代码
            fixed_code = self._generate_fixed_reference_code(key_variables, instruction)

            if self.logger:
                self.logger.info("✅ 代码自动修复完成")

            return fixed_code
        else:
            if self.logger:
                self.logger.info("✅ 代码质量验证通过")
            return code

    def _generate_fixed_reference_code(self, key_variables: list, instruction: str) -> str:
        """
        生成符合指导原则的修复代码
        """
        # 基础导入
        code_parts = [
            "# 基于引用的扩展分析",
            "import pandas as pd",
            "import streamlit as st",
            ""
        ]

        # 确保历史变量存在
        if 'region_sales' in key_variables:
            code_parts.extend([
                "# 确保历史变量存在",
                "if 'region_sales' not in locals():",
                "    region_sales = df.groupby('地区')['销售额'].sum().reset_index()",
                ""
            ])

        # 根据指令生成相应的组合分析
        if '销售员' in instruction:
            code_parts.extend([
                "# 地区+销售员组合分析",
                "region_salesperson = df.groupby(['地区', '销售员'])['销售额'].sum().reset_index()",
                "",
                "st.subheader('🌍 基于地区分析的销售员表现')",
                "",
                "# 展示组合数据",
                "st.write('📊 各地区销售员详细表现:')",
                "st.dataframe(region_salesperson)",
                "",
                "# 按地区展示销售员排名",
                "for region in region_sales['地区']:",
                "    region_data = region_salesperson[region_salesperson['地区'] == region]",
                "    if not region_data.empty:",
                "        st.write(f'📍 {region}地区销售员排名:')",
                "        region_chart_data = region_data.set_index('销售员')['销售额']",
                "        st.bar_chart(region_chart_data)",
                "",
                "# 总体销售员表现",
                "st.write('🏆 销售员总体表现:')",
                "salesperson_total = df.groupby('销售员')['销售额'].sum().sort_values(ascending=False)",
                "st.bar_chart(salesperson_total)"
            ])
        elif '产品' in instruction:
            code_parts.extend([
                "# 地区+产品组合分析",
                "region_product = df.groupby(['地区', '产品名称'])['销售额'].sum().reset_index()",
                "",
                "st.subheader('🌍 基于地区分析的产品表现')",
                "",
                "# 按地区展示产品排名",
                "for region in region_sales['地区']:",
                "    region_data = region_product[region_product['地区'] == region]",
                "    if not region_data.empty:",
                "        st.write(f'📍 {region}地区产品排名:')",
                "        region_chart_data = region_data.set_index('产品名称')['销售额']",
                "        st.bar_chart(region_chart_data)"
            ])
        else:
            # 默认的扩展分析
            code_parts.extend([
                "# 基于地区的扩展分析",
                "st.subheader('🌍 基于地区分析的扩展信息')",
                "",
                "# 展示地区详细信息",
                "for region in region_sales['地区']:",
                "    region_data = df[df['地区'] == region]",
                "    st.write(f'📍 {region}地区详细信息:')",
                "    st.dataframe(region_data)"
            ])

        return "\n".join(code_parts)

    def analyze_data(self, instruction: str, context: str, metadata: Optional[Dict] = None, table_name: str = "data") -> str:
        """
        分析数据并生成代码

        Args:
            instruction: 用户指令
            context: 数据上下文
            metadata: 元数据（可选）
            table_name: 表格名称，用于获取业务元数据

        Returns:
            处理后的Python代码
        """
        if self.logger:
            self.logger.info(f"开始分析数据 - 指令: {instruction[:50]}...")
        
        try:
            # 1. 构建提示词（传递表格名称）
            if self.enable_metadata:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, metadata, table_name)
            else:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, None, table_name)
            
            # 2. 调用LLM API
            response = self.client.call(instruction=prompt, context="")
            raw_code = response.content
            
            if self.logger:
                self.logger.info(f"LLM响应获取成功 - tokens: {response.tokens_used}")
            
            # 3. 清理代码
            cleaned_code = self.code_cleaner.clean(raw_code)
            
            # 4. 修复图表（如果启用）
            if self.enable_chart_fix:
                final_code = self.chart_fixer.fix_charts(cleaned_code, instruction)
            else:
                final_code = cleaned_code
            
            if self.logger:
                self.logger.info("数据分析完成")
            
            return final_code
            
        except Exception as e:
            error_msg = f"数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return f"# {error_msg}\nst.error('{error_msg}')"
    
    def set_chart_fix_enabled(self, enabled: bool):
        """设置图表修复功能开关"""
        self.enable_chart_fix = enabled
        if self.logger:
            self.logger.info(f"图表修复功能: {'启用' if enabled else '禁用'}")
    
    def set_metadata_enabled(self, enabled: bool):
        """设置元数据功能开关"""
        self.enable_metadata = enabled
        if self.logger:
            self.logger.info(f"元数据功能: {'启用' if enabled else '禁用'}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取LLM使用统计"""
        return self.client.get_stats()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.client.get_model_info()

    def _optimize_code_for_context(self, code: str) -> str:
        """
        优化代码用于上下文传递
        保留核心逻辑，移除冗余的展示代码

        Args:
            code: 原始代码

        Returns:
            优化后的代码
        """
        lines = code.split('\n')
        optimized_lines = []

        # 保留的核心逻辑关键词
        core_keywords = [
            'df.groupby', '=', 'sum()', 'mean()', 'count()', 'max()', 'min()',
            'sort_values', 'index[', 'iloc[', 'filter', 'query', 'merge',
            'import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while '
        ]

        # 需要简化的展示代码关键词
        display_keywords = [
            'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
            'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
            'plt.', 'fig.', '.show()', 'print('
        ]

        skipped_display_lines = 0

        for line in lines:
            line_stripped = line.strip()

            # 跳过空行和注释
            if not line_stripped or line_stripped.startswith('#'):
                continue

            # 检查是否是核心逻辑
            is_core_logic = any(keyword in line for keyword in core_keywords)
            is_display_code = any(keyword in line for keyword in display_keywords)

            if is_core_logic:
                optimized_lines.append(line)
            elif is_display_code:
                skipped_display_lines += 1
                # 只保留第一个展示语句作为示例
                if skipped_display_lines == 1:
                    optimized_lines.append(f"# ... 展示逻辑 (共{self._count_display_lines(lines)}行)")
            else:
                # 其他逻辑也保留
                optimized_lines.append(line)

        # 如果优化后的代码太短，返回原代码的前10行
        if len(optimized_lines) < 3:
            return '\n'.join(lines[:10]) + ('\n# ... (代码已截断)' if len(lines) > 10 else '')

        return '\n'.join(optimized_lines)

    def _count_display_lines(self, lines: list) -> int:
        """计算展示代码的行数"""
        display_keywords = [
            'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
            'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
            'plt.', 'fig.', '.show()', 'print('
        ]

        count = 0
        for line in lines:
            if any(keyword in line for keyword in display_keywords):
                count += 1
        return count


class LLMFactory:
    """
    LLM工厂类
    
    负责创建和配置各种LLM实例。
    """
    
    @staticmethod
    def create_tongyi_llm(
        config: Optional[TongyiConfig] = None,
        enable_chart_fix: bool = True,
        enable_metadata: bool = True,  # 默认启用元数据
        enable_logging: bool = True
    ) -> EnhancedTongyiLLM:
        """
        创建增强的通义千问LLM实例
        
        Args:
            config: 通义千问配置（如果为None，从环境变量加载）
            enable_chart_fix: 是否启用图表修复
            enable_metadata: 是否启用元数据支持
            enable_logging: 是否启用日志记录
            
        Returns:
            配置好的增强LLM实例
        """
        # 如果没有提供配置，从环境变量加载
        if config is None:
            config = TongyiConfig.from_env()
        
        # 更新配置中的功能开关
        config.enable_chart_fix = enable_chart_fix
        config.enable_metadata = enable_metadata
        config.enable_logging = enable_logging
        
        # 创建API客户端
        client = TongyiQianwenClient(config)
        
        # 创建处理器
        code_cleaner = CodeCleaner(enable_logging)
        chart_fixer = ChartFixer(enable_logging)
        metadata_processor = MetadataProcessor(enable_logging)
        
        # 创建增强LLM
        enhanced_llm = EnhancedTongyiLLM(
            client=client,
            code_cleaner=code_cleaner,
            chart_fixer=chart_fixer,
            metadata_processor=metadata_processor,
            enable_logging=enable_logging
        )
        
        # 设置功能开关
        enhanced_llm.set_chart_fix_enabled(enable_chart_fix)
        enhanced_llm.set_metadata_enabled(enable_metadata)
        
        return enhanced_llm
    
    @staticmethod
    def create_basic_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建基础的通义千问LLM实例（最小功能）
        
        Args:
            config: 通义千问配置
            
        Returns:
            基础LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=False,
            enable_metadata=False,
            enable_logging=False
        )
    
    @staticmethod
    def create_full_featured_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建全功能的通义千问LLM实例
        
        Args:
            config: 通义千问配置
            
        Returns:
            全功能LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=True,
            enable_metadata=True,
            enable_logging=True
        )
    
    @staticmethod
    def get_available_models() -> Dict[str, Dict[str, Any]]:
        """获取可用的模型列表"""
        return TongyiQianwenClient.RECOMMENDED_MODELS
    
    @staticmethod
    def validate_config(config: TongyiConfig) -> tuple[bool, str]:
        """
        验证配置是否有效
        
        Args:
            config: 要验证的配置
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            client = TongyiQianwenClient(config)
            return client.validate_config(), ""
        except Exception as e:
            return False, str(e)
