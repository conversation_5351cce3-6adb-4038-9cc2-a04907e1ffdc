"""
LLM工厂类 - 负责创建和配置完整的LLM实例
"""

from typing import Optional, Dict, Any, List
from .tongyi_client import TongyiQianwenClient
from ..processors.code_cleaner import CodeCleaner
from ..processors.chart_fixer import ChartFixer
from ..processors.metadata_processor import MetadataProcessor
from ..utils.config import TongyiConfig
from ..utils.logger import get_app_logger


class EnhancedTongyiLLM:
    """
    增强的通义千问LLM
    
    组合了API客户端和各种处理器，提供完整的LLM功能。
    """
    
    def __init__(
        self,
        client: TongyiQianwenClient,
        code_cleaner: Optional[CodeCleaner] = None,
        chart_fixer: Optional[ChartFixer] = None,
        metadata_processor: Optional[MetadataProcessor] = None,
        enable_logging: bool = True
    ):
        """
        初始化增强LLM
        
        Args:
            client: 通义千问API客户端
            code_cleaner: 代码清理器
            chart_fixer: 图表修复器
            metadata_processor: 元数据处理器
            enable_logging: 是否启用日志记录
        """
        self.client = client
        self.code_cleaner = code_cleaner or CodeCleaner(enable_logging)
        self.chart_fixer = chart_fixer or ChartFixer(enable_logging)
        self.metadata_processor = metadata_processor or MetadataProcessor(enable_logging)
        
        self.logger = get_app_logger() if enable_logging else None
        
        # 功能开关
        self.enable_chart_fix = True
        self.enable_metadata = True  # 启用元数据功能
    
    def analyze_data_with_context(self, instruction: str, context: str,
                                 conversation_context: Optional[Dict] = None,
                                 metadata: Optional[Dict] = None,
                                 table_name: str = "data") -> str:
        """
        基于对话上下文的数据分析

        Args:
            instruction: 用户指令
            context: 数据上下文
            conversation_context: 对话上下文信息
            metadata: 元数据
            table_name: 表名

        Returns:
            生成的代码
        """
        if self.logger:
            self.logger.info(f"开始上下文感知的数据分析 - 指令: {instruction[:50]}...")

        try:
            # 1. 构建增强的提示词
            if conversation_context:
                enhanced_prompt = self._build_contextual_prompt(
                    instruction, context, conversation_context, metadata, table_name
                )
            else:
                # 降级到原有方法
                return self.analyze_data(instruction, context, metadata, table_name)

            # 2. 调用LLM API
            response = self.client.call(instruction=enhanced_prompt, context="")
            raw_code = response.content

            if self.logger:
                self.logger.info(f"LLM响应获取成功 - tokens: {response.tokens_used}")

            # 3. 清理代码
            cleaned_code = self.code_cleaner.clean(raw_code)

            # 3.5. 验证引用性代码（新增）
            if conversation_context and conversation_context.get('references'):
                cleaned_code = self._validate_and_fix_reference_code(cleaned_code, conversation_context, instruction)

            # 4. 修复图表（如果启用）
            if self.enable_chart_fix:
                final_code = self.chart_fixer.fix_charts(cleaned_code, instruction)
            else:
                final_code = cleaned_code

            if self.logger:
                self.logger.info("上下文感知数据分析完成")

            return final_code

        except Exception as e:
            error_msg = f"上下文感知数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)

            # 降级到基础分析
            if self.logger:
                self.logger.info("降级到基础数据分析模式")
            return self.analyze_data(instruction, context, metadata, table_name)

    def _build_contextual_prompt(self, instruction: str, context: str,
                               conversation_context: Dict, metadata: Optional[Dict],
                               table_name: str) -> str:
        """
        构建包含对话上下文的提示词

        Args:
            instruction: 当前指令
            context: 数据上下文
            conversation_context: 对话上下文
            metadata: 元数据
            table_name: 表名

        Returns:
            增强的提示词
        """
        prompt_parts = [
            "你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。",
            f"当前数据信息：\n{context}",
        ]

        # 添加元数据信息
        if metadata and self.enable_metadata:
            metadata_text = self.metadata_processor.format_metadata_for_prompt(metadata)
            prompt_parts.append(f"数据元数据：\n{metadata_text}")

        # 添加对话摘要
        if conversation_context.get('has_summary') and conversation_context['summary']:
            summary = conversation_context['summary']
            prompt_parts.append(f"对话背景：{summary['summary_text']}")

            if summary['key_concerns']:
                prompt_parts.append(f"用户关注点：{', '.join(summary['key_concerns'])}")

        # 添加最近对话历史（精简版 - 只保留关键信息）
        recent_rounds = conversation_context.get('recent_rounds', [])
        if recent_rounds:
            prompt_parts.append("最近的对话历史：")
            for i, round_data in enumerate(recent_rounds[-2:], 1):  # 减少到最近2轮
                user_msg = round_data['user_message']
                code = round_data.get('code', '')

                # 提取关键信息而不是完整代码
                key_info = self._extract_key_info_from_code(code)

                prompt_parts.append(f"第{i}轮 - 用户：{user_msg}")
                if key_info['variables']:
                    prompt_parts.append(f"第{i}轮 - 生成变量：{', '.join(key_info['variables'])}")
                if key_info['operations']:
                    prompt_parts.append(f"第{i}轮 - 主要操作：{', '.join(key_info['operations'])}")

                # 添加执行结果状态
                exec_result = round_data.get('execution_result', {})
                if exec_result.get('success'):
                    prompt_parts.append(f"第{i}轮 - 代码执行：成功")
                elif exec_result:
                    prompt_parts.append(f"第{i}轮 - 代码执行：失败")

        # 构建结构化提示词
        structured_prompt = self._build_structured_prompt_sections(
            instruction, context, conversation_context, metadata, table_name
        )
        prompt_parts.extend(structured_prompt)



        final_prompt = "\n\n".join(prompt_parts)

        # 详细日志输出 - 发送给LLM的完整提示词
        if self.logger:
            self.logger.info("=" * 100)
            self.logger.info("🤖 发送给大模型的完整提示词:")
            self.logger.info("-" * 100)
            self.logger.info(final_prompt)
            self.logger.info("-" * 100)
            self.logger.info(f"提示词总长度: {len(final_prompt)} 字符")
            self.logger.info(f"预估Token数: {len(final_prompt) // 4} tokens")
            self.logger.info("=" * 100)

        return final_prompt

    def _validate_and_fix_reference_code(self, code: str, conversation_context: Dict, instruction: str) -> str:
        """
        验证并修复引用性代码，确保符合指导原则
        """
        if self.logger:
            self.logger.info("🔍 开始验证引用性代码质量")

        # 检查是否包含"进一步"等引用关键词
        reference_keywords = ['进一步', '基于', '在此基础上', '然后', '接下来']
        has_reference_intent = any(kw in instruction for kw in reference_keywords)

        if not has_reference_intent:
            return code

        # 获取历史变量信息
        references = conversation_context.get('references', {})
        analysis_ref = references.get('analysis', {})
        historical_code = analysis_ref.get('code', '')

        # 检查历史代码中的关键变量
        key_variables = []
        if 'region_sales' in historical_code:
            key_variables.append('region_sales')
        if 'product_sales' in historical_code:
            key_variables.append('product_sales')

        if not key_variables:
            if self.logger:
                self.logger.warning("⚠️ 未找到历史关键变量，跳过验证")
            return code

        # 验证生成的代码
        validation_issues = []

        # 检查1: 是否复用了历史变量
        has_variable_reuse = False
        for var in key_variables:
            if f"if '{var}' not in locals():" in code or var in code:
                has_variable_reuse = True
                break

        if not has_variable_reuse:
            validation_issues.append(f"❌ 缺少变量复用: 应该复用 {key_variables}")

        # 检查2: 是否实现了组合分析
        has_combination_analysis = False
        combination_patterns = [
            "groupby(['地区', '销售员'])",
            'groupby(["地区", "销售员"])',
            "groupby(['地区','销售员'])",
            'groupby(["地区","销售员"])'
        ]

        for pattern in combination_patterns:
            if pattern in code:
                has_combination_analysis = True
                break

        if not has_combination_analysis and '销售员' in instruction:
            validation_issues.append("❌ 缺少组合分析: 应该使用 groupby(['地区', '销售员'])")

        # 检查3: 是否基于历史变量进行展示
        has_based_display = False
        for var in key_variables:
            if f"for region in {var}['地区']:" in code or f"for region in {var}[\"地区\"]:" in code:
                has_based_display = True
                break

        if not has_based_display and 'region_sales' in key_variables:
            validation_issues.append("❌ 缺少基于展示: 应该使用 for region in region_sales['地区']:")

        # 如果有验证问题，尝试修复
        if validation_issues:
            if self.logger:
                self.logger.warning(f"🚨 发现代码质量问题: {len(validation_issues)}个")
                for issue in validation_issues:
                    self.logger.warning(f"   {issue}")
                self.logger.info("🔧 尝试自动修复代码...")

            # 生成修复后的代码
            fixed_code = self._generate_fixed_reference_code(key_variables, instruction)

            if self.logger:
                self.logger.info("✅ 代码自动修复完成")

            return fixed_code
        else:
            if self.logger:
                self.logger.info("✅ 代码质量验证通过")
            return code

    def _generate_fixed_reference_code(self, key_variables: list, instruction: str) -> str:
        """
        生成符合指导原则的修复代码
        """
        # 基础导入
        code_parts = [
            "# 基于引用的扩展分析",
            "import pandas as pd",
            "import streamlit as st",
            ""
        ]

        # 确保历史变量存在
        if 'region_sales' in key_variables:
            code_parts.extend([
                "# 确保历史变量存在",
                "if 'region_sales' not in locals():",
                "    region_sales = df.groupby('地区')['销售额'].sum().reset_index()",
                ""
            ])

        # 根据指令生成相应的组合分析
        if '销售员' in instruction:
            code_parts.extend([
                "# 地区+销售员组合分析",
                "region_salesperson = df.groupby(['地区', '销售员'])['销售额'].sum().reset_index()",
                "",
                "st.subheader('🌍 基于地区分析的销售员表现')",
                "",
                "# 展示组合数据",
                "st.write('📊 各地区销售员详细表现:')",
                "st.dataframe(region_salesperson)",
                "",
                "# 按地区展示销售员排名",
                "for region in region_sales['地区']:",
                "    region_data = region_salesperson[region_salesperson['地区'] == region]",
                "    if not region_data.empty:",
                "        st.write(f'📍 {region}地区销售员排名:')",
                "        region_chart_data = region_data.set_index('销售员')['销售额']",
                "        st.bar_chart(region_chart_data)",
                "",
                "# 总体销售员表现",
                "st.write('🏆 销售员总体表现:')",
                "salesperson_total = df.groupby('销售员')['销售额'].sum().sort_values(ascending=False)",
                "st.bar_chart(salesperson_total)"
            ])
        elif '产品' in instruction:
            code_parts.extend([
                "# 地区+产品组合分析",
                "region_product = df.groupby(['地区', '产品名称'])['销售额'].sum().reset_index()",
                "",
                "st.subheader('🌍 基于地区分析的产品表现')",
                "",
                "# 按地区展示产品排名",
                "for region in region_sales['地区']:",
                "    region_data = region_product[region_product['地区'] == region]",
                "    if not region_data.empty:",
                "        st.write(f'📍 {region}地区产品排名:')",
                "        region_chart_data = region_data.set_index('产品名称')['销售额']",
                "        st.bar_chart(region_chart_data)"
            ])
        else:
            # 默认的扩展分析
            code_parts.extend([
                "# 基于地区的扩展分析",
                "st.subheader('🌍 基于地区分析的扩展信息')",
                "",
                "# 展示地区详细信息",
                "for region in region_sales['地区']:",
                "    region_data = df[df['地区'] == region]",
                "    st.write(f'📍 {region}地区详细信息:')",
                "    st.dataframe(region_data)"
            ])

        return "\n".join(code_parts)

    def analyze_data(self, instruction: str, context: str, metadata: Optional[Dict] = None, table_name: str = "data") -> str:
        """
        分析数据并生成代码

        Args:
            instruction: 用户指令
            context: 数据上下文
            metadata: 元数据（可选）
            table_name: 表格名称，用于获取业务元数据

        Returns:
            处理后的Python代码
        """
        if self.logger:
            self.logger.info(f"开始分析数据 - 指令: {instruction[:50]}...")
        
        try:
            # 1. 构建提示词（传递表格名称）
            if self.enable_metadata:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, metadata, table_name)
            else:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, None, table_name)
            
            # 2. 调用LLM API
            response = self.client.call(instruction=prompt, context="")
            raw_code = response.content
            
            if self.logger:
                self.logger.info(f"LLM响应获取成功 - tokens: {response.tokens_used}")
            
            # 3. 清理代码
            cleaned_code = self.code_cleaner.clean(raw_code)
            
            # 4. 修复图表（如果启用）
            if self.enable_chart_fix:
                final_code = self.chart_fixer.fix_charts(cleaned_code, instruction)
            else:
                final_code = cleaned_code
            
            if self.logger:
                self.logger.info("数据分析完成")
            
            return final_code
            
        except Exception as e:
            error_msg = f"数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return f"# {error_msg}\nst.error('{error_msg}')"
    
    def set_chart_fix_enabled(self, enabled: bool):
        """设置图表修复功能开关"""
        self.enable_chart_fix = enabled
        if self.logger:
            self.logger.info(f"图表修复功能: {'启用' if enabled else '禁用'}")
    
    def set_metadata_enabled(self, enabled: bool):
        """设置元数据功能开关"""
        self.enable_metadata = enabled
        if self.logger:
            self.logger.info(f"元数据功能: {'启用' if enabled else '禁用'}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取LLM使用统计"""
        return self.client.get_stats()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.client.get_model_info()

    def _extract_key_info_from_code(self, code: str) -> Dict[str, List[str]]:
        """
        从代码中提取关键信息，替代完整代码粘贴

        Args:
            code: 原始代码

        Returns:
            包含变量和操作的字典
        """
        if not code:
            return {'variables': [], 'operations': []}

        import re

        # 提取变量名（更精确的正则）
        variables = []
        var_patterns = [
            r'(\w+)\s*=\s*df\.groupby',  # 分组变量
            r'(\w+)\s*=\s*df\[',         # 筛选变量
            r'(\w+)\s*=\s*\w+\.sum\(\)', # 聚合变量
            r'(\w+)\s*=\s*\w+\.mean\(\)', # 平均值变量
            r'(\w+)\s*=\s*\w+\.reset_index\(\)', # 重置索引变量
        ]

        for pattern in var_patterns:
            matches = re.findall(pattern, code)
            variables.extend(matches)

        # 提取关键操作
        operations = []
        if 'groupby' in code.lower():
            # 提取分组字段
            groupby_matches = re.findall(r'groupby\([\'"]([^\'"]+)[\'"]', code)
            if groupby_matches:
                operations.append(f"按{groupby_matches[0]}分组")
            else:
                # 检查多字段分组
                multi_groupby = re.findall(r'groupby\(\[([^\]]+)\]', code)
                if multi_groupby:
                    fields = [f.strip().strip('\'"') for f in multi_groupby[0].split(',')]
                    operations.append(f"按{'+'.join(fields)}分组")
                else:
                    operations.append("数据分组")

        if '.sum()' in code:
            operations.append("求和计算")
        if '.mean()' in code:
            operations.append("平均值计算")
        if '.count()' in code:
            operations.append("计数统计")
        if '.max()' in code:
            operations.append("最大值计算")
        if '.min()' in code:
            operations.append("最小值计算")
        if 'chart' in code.lower() or 'plot' in code.lower():
            operations.append("图表可视化")
        if 'st.dataframe' in code or 'st.table' in code:
            operations.append("表格展示")
        if 'st.subheader' in code or 'st.write' in code:
            operations.append("结果展示")

        # 去重并保持顺序
        variables = list(dict.fromkeys(variables))
        operations = list(dict.fromkeys(operations))

        return {'variables': variables, 'operations': operations}

    def _build_structured_prompt_sections(self, instruction: str, context: str,
                                         conversation_context: Dict, metadata: Optional[Dict],
                                         table_name: str) -> List[str]:
        """
        构建结构化的提示词各个部分

        Args:
            instruction: 用户指令
            context: 数据上下文
            conversation_context: 对话上下文
            metadata: 元数据
            table_name: 表名

        Returns:
            结构化提示词部分列表
        """
        sections = []

        # 1. 数据信息部分
        sections.extend([
            "=" * 60,
            "📊 **数据信息**",
            "=" * 60,
            context,
            ""
        ])

        # 2. 元数据部分（如果有）
        if metadata:
            sections.extend([
                "=" * 60,
                "🏷️ **数据元数据**",
                "=" * 60,
                self._format_metadata_for_prompt(metadata),
                ""
            ])

        # 3. 对话上下文分析
        context_analysis = self._analyze_conversation_context(instruction, conversation_context)
        if context_analysis['has_context']:
            sections.extend([
                "=" * 60,
                "🔄 **对话上下文**",
                "=" * 60,
                f"**业务背景**: {context_analysis['business_context']}",
                f"**引用类型**: {context_analysis['reference_type']} (置信度: {context_analysis['confidence']:.2f})",
                ""
            ])

            if context_analysis['key_variables']:
                sections.append(f"**可用变量**: {', '.join(context_analysis['key_variables'])}")
            if context_analysis['key_operations']:
                sections.append(f"**历史操作**: {', '.join(context_analysis['key_operations'])}")
            sections.append("")

        # 4. 当前任务部分
        sections.extend([
            "=" * 60,
            "🎯 **当前任务**",
            "=" * 60,
            f"**用户指令**: {instruction}",
            ""
        ])

        # 5. 智能指导原则
        guidance = self._generate_intelligent_guidance(context_analysis, instruction)
        sections.extend([
            "=" * 60,
            "💡 **分析指导**",
            "=" * 60
        ])
        sections.extend(guidance)
        sections.append("")

        # 6. 代码生成要求
        sections.extend([
            "=" * 60,
            "📝 **代码生成要求**",
            "=" * 60,
            "请基于以上信息生成相应的Python代码，确保代码能够独立运行并使用Streamlit组件展示结果。",
            ""
        ])

        return sections

    def _analyze_conversation_context(self, instruction: str, conversation_context: Dict) -> Dict[str, Any]:
        """
        分析对话上下文，检测引用关系和业务背景
        """
        recent_rounds = conversation_context.get('recent_rounds', [])

        if not recent_rounds:
            return {
                'has_context': False,
                'business_context': '这是一个新的分析任务',
                'reference_type': 'independent',
                'confidence': 0.0,
                'key_variables': [],
                'key_operations': []
            }

        # 提取关键信息
        all_variables = []
        all_operations = []
        business_topics = []

        for round_data in recent_rounds:
            user_msg = round_data.get('user_message', '')
            code = round_data.get('code', '')

            # 提取变量和操作
            key_info = self._extract_key_info_from_code(code)
            all_variables.extend(key_info['variables'])
            all_operations.extend(key_info['operations'])

            # 提取业务主题
            if '地区' in user_msg:
                business_topics.append('地区分析')
            if '销售' in user_msg:
                business_topics.append('销售分析')
            if '产品' in user_msg:
                business_topics.append('产品分析')
            if '销售员' in user_msg:
                business_topics.append('销售员分析')

        # 去重
        all_variables = list(dict.fromkeys(all_variables))
        all_operations = list(dict.fromkeys(all_operations))
        business_topics = list(set(business_topics))

        # 检测引用类型和置信度
        reference_info = self._detect_reference_type_and_confidence(instruction, recent_rounds)

        # 生成业务背景描述
        if business_topics:
            business_context = f"用户正在进行{', '.join(business_topics)}，当前希望{instruction}"
        else:
            business_context = f"用户正在进行数据分析，当前希望{instruction}"

        return {
            'has_context': True,
            'business_context': business_context,
            'reference_type': reference_info['type'],
            'confidence': reference_info['confidence'],
            'key_variables': all_variables,
            'key_operations': all_operations
        }

    def _detect_reference_type_and_confidence(self, instruction: str, recent_rounds: List[Dict]) -> Dict[str, Any]:
        """
        检测引用类型和置信度
        """
        instruction_lower = instruction.lower()

        # 语义模式匹配
        reference_patterns = {
            'continuation': {
                'patterns': [
                    r'(在.*基础上|基于.*|根据.*|参考.*)',
                    r'(进一步.*|深入.*|详细.*|具体.*)',
                    r'(接着.*|然后.*|继续.*)'
                ],
                'confidence_base': 0.9
            },
            'modification': {
                'patterns': [
                    r'(修改.*|调整.*|改变.*|优化.*)',
                    r'(重新.*|再次.*|重做.*)',
                    r'(换成.*|改为.*|变成.*)'
                ],
                'confidence_base': 0.95
            },
            'comparison': {
                'patterns': [
                    r'(对比.*|比较.*|对照.*)',
                    r'(差异.*|区别.*|不同.*)',
                    r'(相比.*|与.*比较)'
                ],
                'confidence_base': 0.9
            },
            'extension': {
                'patterns': [
                    r'(同时.*|另外.*|此外.*|还要.*)',
                    r'(加上.*|增加.*|补充.*)',
                    r'(以及.*|和.*一起)'
                ],
                'confidence_base': 0.8
            }
        }

        best_type = 'independent'
        max_confidence = 0.0

        for ref_type, config in reference_patterns.items():
            confidence = 0.0
            pattern_matches = 0

            for pattern in config['patterns']:
                import re
                if re.search(pattern, instruction_lower):
                    pattern_matches += 1

            if pattern_matches > 0:
                confidence = config['confidence_base'] * (pattern_matches / len(config['patterns']))

                # 上下文验证：检查是否真的有可引用的内容
                if recent_rounds and self._has_referenceable_content(recent_rounds):
                    confidence += 0.1  # 奖励分

                if confidence > max_confidence:
                    max_confidence = confidence
                    best_type = ref_type

        return {
            'type': best_type,
            'confidence': max_confidence
        }

    def _has_referenceable_content(self, recent_rounds: List[Dict]) -> bool:
        """
        检查是否有可引用的内容
        """
        for round_data in recent_rounds:
            if round_data.get('code') and round_data.get('execution_result', {}).get('success'):
                return True
        return False

    def _generate_intelligent_guidance(self, context_analysis: Dict, instruction: str) -> List[str]:
        """
        生成智能指导原则，替代强制模板
        """
        guidance = []

        if context_analysis['has_context'] and context_analysis['confidence'] >= 0.5:
            ref_type = context_analysis['reference_type']

            if ref_type == 'continuation':
                guidance.append("🔗 **延续性分析**: 用户希望基于之前的分析结果进行深入分析")
                if context_analysis['key_variables']:
                    guidance.append(f"💡 **变量复用**: 可以直接使用已存在的变量: {', '.join(context_analysis['key_variables'][:3])}")
                guidance.append("📋 **分析建议**: 在现有分析基础上增加新的维度或深度")
                guidance.append("🔧 **技术提示**: 考虑使用多维度分组，如 `df.groupby(['维度1', '维度2'])`")

            elif ref_type == 'modification':
                guidance.append("🔧 **修改优化**: 用户希望调整或改进之前的分析")
                guidance.append("💡 **改进方向**: 保持核心逻辑，优化展示方式或分析角度")
                guidance.append("🎨 **展示建议**: 尝试不同的图表类型或数据展示格式")

            elif ref_type == 'comparison':
                guidance.append("⚖️ **对比分析**: 用户希望进行比较分析")
                guidance.append("💡 **对比建议**: 使用相同的分析方法对不同维度进行对比")
                guidance.append("📊 **可视化提示**: 考虑使用并排图表或对比表格")

            elif ref_type == 'extension':
                guidance.append("➕ **扩展分析**: 用户希望在现有基础上增加新内容")
                guidance.append("💡 **扩展建议**: 保持现有分析，同时添加新的分析维度")
                guidance.append("🔄 **整合提示**: 将新分析与历史结果有机结合")
        else:
            guidance.append("🆕 **独立分析**: 这是一个新的分析任务")
            guidance.append("💡 **分析建议**: 从数据探索开始，提供全面的分析")
            guidance.append("📈 **展示建议**: 选择最适合数据特征的可视化方式")

        # 添加通用技术指导
        guidance.extend([
            "",
            "🛠️ **技术要求**:",
            "  - 确保代码能够独立运行",
            "  - 使用Streamlit组件展示结果",
            "  - 代码简洁高效，注重可读性",
            "  - 添加适当的标题和说明文字"
        ])

        return guidance

    def _format_metadata_for_prompt(self, metadata: Dict) -> str:
        """
        格式化元数据用于提示词
        """
        if not metadata:
            return ""

        parts = []
        if 'columns' in metadata:
            parts.append(f"列数: {len(metadata['columns'])}")
        if 'shape' in metadata:
            parts.append(f"数据形状: {metadata['shape']}")
        if 'dtypes' in metadata:
            parts.append("数据类型: " + ", ".join([f"{k}({v})" for k, v in metadata['dtypes'].items()]))

        return " | ".join(parts)

    def _optimize_code_for_context(self, code: str) -> str:
        """
        优化代码用于上下文传递
        保留核心逻辑，移除冗余的展示代码

        Args:
            code: 原始代码

        Returns:
            优化后的代码
        """
        lines = code.split('\n')
        optimized_lines = []

        # 保留的核心逻辑关键词
        core_keywords = [
            'df.groupby', '=', 'sum()', 'mean()', 'count()', 'max()', 'min()',
            'sort_values', 'index[', 'iloc[', 'filter', 'query', 'merge',
            'import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while '
        ]

        # 需要简化的展示代码关键词
        display_keywords = [
            'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
            'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
            'plt.', 'fig.', '.show()', 'print('
        ]

        skipped_display_lines = 0

        for line in lines:
            line_stripped = line.strip()

            # 跳过空行和注释
            if not line_stripped or line_stripped.startswith('#'):
                continue

            # 检查是否是核心逻辑
            is_core_logic = any(keyword in line for keyword in core_keywords)
            is_display_code = any(keyword in line for keyword in display_keywords)

            if is_core_logic:
                optimized_lines.append(line)
            elif is_display_code:
                skipped_display_lines += 1
                # 只保留第一个展示语句作为示例
                if skipped_display_lines == 1:
                    optimized_lines.append(f"# ... 展示逻辑 (共{self._count_display_lines(lines)}行)")
            else:
                # 其他逻辑也保留
                optimized_lines.append(line)

        # 如果优化后的代码太短，返回原代码的前10行
        if len(optimized_lines) < 3:
            return '\n'.join(lines[:10]) + ('\n# ... (代码已截断)' if len(lines) > 10 else '')

        return '\n'.join(optimized_lines)

    def _count_display_lines(self, lines: list) -> int:
        """计算展示代码的行数"""
        display_keywords = [
            'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
            'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
            'plt.', 'fig.', '.show()', 'print('
        ]

        count = 0
        for line in lines:
            if any(keyword in line for keyword in display_keywords):
                count += 1
        return count


class LLMFactory:
    """
    LLM工厂类
    
    负责创建和配置各种LLM实例。
    """
    
    @staticmethod
    def create_tongyi_llm(
        config: Optional[TongyiConfig] = None,
        enable_chart_fix: bool = True,
        enable_metadata: bool = True,  # 默认启用元数据
        enable_logging: bool = True
    ) -> EnhancedTongyiLLM:
        """
        创建增强的通义千问LLM实例
        
        Args:
            config: 通义千问配置（如果为None，从环境变量加载）
            enable_chart_fix: 是否启用图表修复
            enable_metadata: 是否启用元数据支持
            enable_logging: 是否启用日志记录
            
        Returns:
            配置好的增强LLM实例
        """
        # 如果没有提供配置，从环境变量加载
        if config is None:
            config = TongyiConfig.from_env()
        
        # 更新配置中的功能开关
        config.enable_chart_fix = enable_chart_fix
        config.enable_metadata = enable_metadata
        config.enable_logging = enable_logging
        
        # 创建API客户端
        client = TongyiQianwenClient(config)
        
        # 创建处理器
        code_cleaner = CodeCleaner(enable_logging)
        chart_fixer = ChartFixer(enable_logging)
        metadata_processor = MetadataProcessor(enable_logging)
        
        # 创建增强LLM
        enhanced_llm = EnhancedTongyiLLM(
            client=client,
            code_cleaner=code_cleaner,
            chart_fixer=chart_fixer,
            metadata_processor=metadata_processor,
            enable_logging=enable_logging
        )
        
        # 设置功能开关
        enhanced_llm.set_chart_fix_enabled(enable_chart_fix)
        enhanced_llm.set_metadata_enabled(enable_metadata)
        
        return enhanced_llm
    
    @staticmethod
    def create_basic_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建基础的通义千问LLM实例（最小功能）
        
        Args:
            config: 通义千问配置
            
        Returns:
            基础LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=False,
            enable_metadata=False,
            enable_logging=False
        )
    
    @staticmethod
    def create_full_featured_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建全功能的通义千问LLM实例
        
        Args:
            config: 通义千问配置
            
        Returns:
            全功能LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=True,
            enable_metadata=True,
            enable_logging=True
        )
    
    @staticmethod
    def get_available_models() -> Dict[str, Dict[str, Any]]:
        """获取可用的模型列表"""
        return TongyiQianwenClient.RECOMMENDED_MODELS
    
    @staticmethod
    def validate_config(config: TongyiConfig) -> tuple[bool, str]:
        """
        验证配置是否有效
        
        Args:
            config: 要验证的配置
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            client = TongyiQianwenClient(config)
            return client.validate_config(), ""
        except Exception as e:
            return False, str(e)
